#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新功能的核心函数
"""

import sqlite3
from datetime import datetime

# 配置
DB_FILE = "test_zsjt_notifications.db"
NOTICE_TYPES = {
    "0": {"name": "水路客运", "emoji": "🚢", "enabled": True},
    "1": {"name": "交通信息", "emoji": "🚗", "enabled": True},
    "2": {"name": "天气预警", "emoji": "🌦️", "enabled": True}
}

def init_database():
    """初始化数据库"""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()
    
    # 创建通知记录表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS notifications (
            id TEXT PRIMARY KEY,
            notice_type TEXT NOT NULL,
            title TEXT NOT NULL,
            abstracts TEXT,
            publish_date TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            discovery_order INTEGER
        )
    ''')
    
    # 创建发送记录表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS sent_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT NOT NULL,
            notice_type TEXT NOT NULL,
            notice_count INTEGER NOT NULL,
            sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    conn.commit()
    conn.close()
    print("✅ 数据库初始化成功")

def test_save_notification():
    """测试保存通知功能"""
    date_str = datetime.now().strftime('%Y-%m-%d')
    
    # 模拟通知数据
    test_notices = [
        {
            'id': 'test_001',
            'title': '测试水路客运通知',
            'abstracts': '这是一个测试通知的摘要内容',
            'publishDate': '2025-01-25 10:30:00'
        },
        {
            'id': 'test_002', 
            'title': '测试交通信息通知',
            'abstracts': '这是另一个测试通知的摘要内容',
            'publishDate': '2025-01-25 11:00:00'
        }
    ]
    
    # 保存水路客运通知
    save_notifications_to_db(test_notices[:1], "0", date_str)
    print("✅ 水路客运通知保存成功")
    
    # 保存交通信息通知
    save_notifications_to_db(test_notices[1:], "1", date_str)
    print("✅ 交通信息通知保存成功")

def save_notifications_to_db(notices, notice_type, date_str):
    """保存通知到数据库"""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()
    
    # 获取当前最大的发现顺序号
    cursor.execute('SELECT MAX(discovery_order) FROM notifications WHERE date(created_at) = ?', (date_str,))
    max_order = cursor.fetchone()[0] or 0
    
    for notice in notices:
        max_order += 1
        cursor.execute('''
            INSERT OR REPLACE INTO notifications 
            (id, notice_type, title, abstracts, publish_date, discovery_order)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            notice.get('id'),
            notice_type,
            notice.get('title', ''),
            notice.get('abstracts', ''),
            notice.get('publishDate', ''),
            max_order
        ))
    
    conn.commit()
    conn.close()

def test_format_message():
    """测试消息格式化功能"""
    # 模拟从数据库获取的通知
    test_notices = [
        {
            'title': '测试水路客运通知',
            'publishDate': '2025-01-25 10:30:00',
            'abstracts': '这是一个测试通知的摘要内容',
            'discovery_order': 1
        }
    ]
    
    message = format_type_messages(test_notices, "0")
    print("\n📱 格式化的微信消息:")
    print(message)
    print("\n✅ 消息格式化测试成功")

def format_type_messages(notices, notice_type):
    """格式化指定类型的通知为微信消息"""
    if not notices:
        return None
    
    type_info = NOTICE_TYPES.get(notice_type, {"name": "未知类型", "emoji": "📋"})
    current_time = datetime.now()
    
    message = f"{type_info['emoji']}【{current_time.strftime('%m-%d')}】{type_info['name']}通知\n📊 共 {len(notices)} 条\n"
    message += "=" * 25 + "\n"
    
    for notice in notices:
        message += f"{notice['discovery_order']}. 【{notice['title']}】\n"
        message += f"发布时间: {notice['publishDate']}\n"
        message += f"内容摘要: {notice['abstracts']}\n"
        message += "-" * 25 + "\n"
    
    return message.rstrip("-" * 25 + "\n")

def test_query_notifications():
    """测试查询通知功能"""
    date_str = datetime.now().strftime('%Y-%m-%d')
    
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT id, notice_type, title, discovery_order
        FROM notifications 
        WHERE date(created_at) = ?
        ORDER BY discovery_order
    ''', (date_str,))
    
    rows = cursor.fetchall()
    conn.close()
    
    print(f"\n📊 今天的通知记录 ({len(rows)} 条):")
    for row in rows:
        type_name = NOTICE_TYPES.get(row[1], {}).get('name', '未知类型')
        print(f"  {row[3]}. [{type_name}] {row[2]}")
    
    print("\n✅ 查询通知测试成功")

if __name__ == "__main__":
    print("🧪 开始测试新功能...")
    print("=" * 50)
    
    try:
        # 初始化数据库
        init_database()
        
        # 测试保存通知
        test_save_notification()
        
        # 测试查询通知
        test_query_notifications()
        
        # 测试消息格式化
        test_format_message()
        
        print("\n🎉 所有测试通过!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()