#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看数据库内容的工具脚本
"""

import sqlite3
from datetime import datetime

DB_FILE = "zsjt_notifications.db"

def check_database():
    """检查数据库内容"""
    try:
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        print("=" * 50)
        print("📊 数据库内容检查")
        print("=" * 50)
        
        # 检查表结构
        print("\n🏗️ 表结构:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        for table in tables:
            print(f"  📋 {table[0]}")
            
        # 检查 sent_records 表结构
        print("\n📝 sent_records 表结构:")
        cursor.execute("PRAGMA table_info(sent_records);")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
            
        # 查看发送记录
        print("\n📱 发送记录:")
        cursor.execute("""
            SELECT id, date, notice_type, notice_count, 
                   substr(message_content, 1, 50) as content_preview,
                   sent_at 
            FROM sent_records 
            ORDER BY sent_at DESC 
            LIMIT 10
        """)
        records = cursor.fetchall()
        
        if records:
            for record in records:
                print(f"  ID: {record[0]}")
                print(f"  日期: {record[1]}")
                print(f"  类型: {record[2]}")
                print(f"  数量: {record[3]}")
                print(f"  内容预览: {record[4]}...")
                print(f"  发送时间: {record[5]}")
                print("  " + "-" * 30)
        else:
            print("  暂无发送记录")
            
        # 查看通知记录统计
        print("\n📊 通知统计:")
        cursor.execute("""
            SELECT notice_type, COUNT(*) as count
            FROM notifications 
            WHERE date(created_at) = date('now')
            GROUP BY notice_type
        """)
        stats = cursor.fetchall()
        
        if stats:
            for stat in stats:
                type_names = {"0": "水路客运", "1": "交通信息", "2": "天气预警"}
                type_name = type_names.get(stat[0], f"类型{stat[0]}")
                print(f"  {type_name}: {stat[1]} 条")
        else:
            print("  今天暂无通知记录")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库时出错: {e}")

if __name__ == "__main__":
    check_database()
