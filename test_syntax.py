#!/usr/bin/env python3
# -*- coding: utf-8 -*-

try:
    # 测试语法
    with open('zsjt_send_wechat_new.py', 'r', encoding='utf-8') as f:
        code = f.read()
    
    compile(code, 'zsjt_send_wechat_new.py', 'exec')
    print("✅ 语法检查通过")
    
    # 测试导入
    import zsjt_send_wechat_new
    print("✅ 模块导入成功")
    
except SyntaxError as e:
    print(f"❌ 语法错误: {e}")
    print(f"   文件: {e.filename}")
    print(f"   行号: {e.lineno}")
    print(f"   位置: {e.offset}")
    print(f"   文本: {e.text}")
except Exception as e:
    print(f"❌ 其他错误: {e}")
    import traceback
    traceback.print_exc()