#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
舟山交通数据自动获取并发送微信
适合定时任务使用，无需用户交互
支持水路客运、交通信息、天气预警三种类型的通知
"""

import requests
import warnings
from datetime import datetime
import json
import os
import time
import sqlite3
from typing import List, Dict, Optional

# 禁用SSL警告
warnings.filterwarnings('ignore', message='Unverified HTTPS request')
requests.packages.urllib3.disable_warnings()

# 配置常量
CHECK_INTERVAL = 300  # 检测间隔时间（秒），默认1分钟
CHECK_START_HOUR = 7   # 开始检测时间（小时），默认7点
CHECK_END_HOUR = 24    # 结束检测时间（小时），默认18点

# 通知类型配置
NOTICE_TYPES = {
    "0": {"name": "水路客运", "emoji": "🚢", "enabled": True},
    "1": {"name": "交通信息", "emoji": "🚗", "enabled": True},
    "2": {"name": "天气预警", "emoji": "🌦️", "enabled": False}
}

# 数据库文件名
DB_FILE = "zsjt_notifications.db"

def xiaowang(content):
    """发送微信消息"""
    url = 'http://10.70.230.46:5003/robot_mothod/api/robot'
    data = {
        'wxid': 'huabaifeng',
        'data_type': 'Text',
        'data': content
    }
    
    try:
        res = requests.post(url, data=data, timeout=10)
        return res.json()
    except Exception as e:
        print(f"发送微信消息失败: {e}")
        return {"error": str(e)}

def get_transport_data(notice_type: str) -> Optional[List[Dict]]:
    """获取舟山交通数据
    
    Args:
        notice_type: 通知类型 ("0":水路客运, "1":交通信息, "2":天气预警)
    
    Returns:
        通知列表或None
    """
    url = "https://cxfwzlb.zsjtw.zhoushan.gov.cn/spaceServer/index/information/getNoticeListV2"
    
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    # 注意：userToken可能会过期，如果请求失败可能需要更新这个值
    data = {
        "h": {
            "deviceId": "fixedDeviceID",
            "userToken": "",
            "appCode": "330900",
            "codeValue": "330900",
            "sourceCodeValue": "330900",
            "appVersion": "0.0.3",
            "deviceTypeName": "wechatmini"
        },
        "b": {
            "page": 1,
            "offset": 30,  # 获取最新30条
            "noticeType": notice_type
        }
    }
    
    try:
        # 跳过SSL证书验证
        response = requests.post(url, headers=headers, json=data, timeout=15, verify=False)
        result = response.json()
        
        if result.get('returnFlag') == '200':
            return result['data']['list']
        else:
            print(f"API返回错误 (类型{notice_type}): {result.get('returnInfo')}")
            return None
            
    except Exception as e:
        print(f"请求出错 (类型{notice_type}): {e}")
        return None

def init_database():
    """初始化数据库"""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()
    
    # 创建通知记录表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS notifications (
            id TEXT PRIMARY KEY,
            notice_type TEXT NOT NULL,
            title TEXT NOT NULL,
            abstracts TEXT,
            publish_date TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            discovery_order INTEGER
        )
    ''')
    
    # 创建发送记录表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS sent_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT NOT NULL,
            notice_type TEXT NOT NULL,
            notice_count INTEGER NOT NULL,
            sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    conn.commit()
    conn.close()

def save_notifications_to_db(notices: List[Dict], notice_type: str, date_str: str):
    """保存通知到数据库"""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()
    
    # 获取当前最大的发现顺序号
    cursor.execute('SELECT MAX(discovery_order) FROM notifications WHERE date(created_at) = ?', (date_str,))
    max_order = cursor.fetchone()[0] or 0
    
    for notice in notices:
        max_order += 1
        cursor.execute('''
            INSERT OR REPLACE INTO notifications 
            (id, notice_type, title, abstracts, publish_date, discovery_order)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            notice.get('id'),
            notice_type,
            notice.get('title', ''),
            notice.get('abstracts', ''),
            notice.get('publishDate', ''),
            max_order
        ))
    
    conn.commit()
    conn.close()

def get_today_notifications_from_db(date_str: str) -> Dict[str, List[Dict]]:
    """从数据库获取今天的通知，按类型分组"""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT id, notice_type, title, abstracts, publish_date, discovery_order
        FROM notifications 
        WHERE date(created_at) = ?
        ORDER BY discovery_order
    ''', (date_str,))
    
    rows = cursor.fetchall()
    conn.close()
    
    # 按类型分组
    result = {"0": [], "1": [], "2": []}
    for row in rows:
        notice = {
            'id': row[0],
            'notice_type': row[1],
            'title': row[2],
            'abstracts': row[3],
            'publishDate': row[4],
            'discovery_order': row[5]
        }
        result[row[1]].append(notice)
    
    return result

def has_sent_today(date_str: str, notice_type: str) -> bool:
    """检查今天是否已发送过指定类型的通知"""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT COUNT(*) FROM sent_records 
        WHERE date = ? AND notice_type = ?
    ''', (date_str, notice_type))
    
    count = cursor.fetchone()[0]
    conn.close()
    
    return count > 0

def mark_as_sent(date_str: str, notice_type: str, notice_count: int):
    """标记为已发送"""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()
    
    cursor.execute('''
        INSERT INTO sent_records (date, notice_type, notice_count)
        VALUES (?, ?, ?)
    ''', (date_str, notice_type, notice_count))
    
    conn.commit()
    conn.close()

def format_type_messages(notices: List[Dict], notice_type: str) -> str:
    """格式化指定类型的通知为微信消息"""
    if not notices:
        return None

    type_info = NOTICE_TYPES.get(notice_type, {"name": "未知类型", "emoji": "📋"})
    current_time = datetime.now()

    message = f"{type_info['emoji']}【{current_time.strftime('%m-%d')}】{type_info['name']}通知\n📊 共 {len(notices)} 条\n"
    message += "=" * 25 + "\n"

    for i, notice in enumerate(notices, 1):
        # 使用discovery_order字段（如果存在），否则使用序号
        order_num = notice.get('discovery_order', i)
        message += f"{order_num}. 【{notice.get('title', '无标题')}】\n"
        message += f"发布时间: {notice.get('publishDate', '未知时间')}\n"
        message += f"内容摘要: {notice.get('abstracts', '无摘要')}\n"
        message += "-" * 25 + "\n"

    return message.rstrip("-" * 25 + "\n")

def is_today(publish_date):
    """判断发布时间是否为今天"""
    try:
        # 解析发布时间 "2025-07-24 08:58:46"
        pub_date = datetime.strptime(publish_date, "%Y-%m-%d %H:%M:%S")
        today = datetime.now().date()
        return pub_date.date() == today
    except:
        return False

def get_all_enabled_notice_types() -> List[str]:
    """获取所有启用的通知类型"""
    return [notice_type for notice_type, config in NOTICE_TYPES.items() if config.get('enabled', False)]

def filter_today_notices(notices):
    """筛选今天的通知并按发布时间排序"""
    today_notices = []
    for notice in notices:
        if is_today(notice.get('publishDate', '')):
            today_notices.append(notice)

    # 按发布时间排序（最新的在前）
    today_notices.sort(key=lambda x: x.get('publishDate', ''), reverse=True)
    return today_notices

def is_in_check_time():
    """判断当前时间是否在检测时间范围内"""
    current_hour = datetime.now().hour
    return CHECK_START_HOUR <= current_hour < CHECK_END_HOUR

def get_next_check_time():
    """获取下次检测时间的描述"""
    current_hour = datetime.now().hour
    if current_hour < CHECK_START_HOUR:
        return f"将在 {CHECK_START_HOUR}:00 开始检测"
    elif current_hour >= CHECK_END_HOUR:
        return f"明天 {CHECK_START_HOUR}:00 开始检测"
    else:
        return f"检测时间内 ({CHECK_START_HOUR}:00-{CHECK_END_HOUR}:00)"

def check_and_send():
    """单次检测并发送函数"""
    current_time = datetime.now()
    date_str = current_time.strftime('%Y-%m-%d')
    
    # 初始化数据库
    init_database()

    print(f"[{current_time.strftime('%Y-%m-%d %H:%M:%S')}] 开始获取舟山交通数据...")

    # 获取所有启用的通知类型
    enabled_types = get_all_enabled_notice_types()
    
    new_notifications_found = False
    
    for notice_type in enabled_types:
        type_name = NOTICE_TYPES[notice_type]['name']
        print(f"🔍 正在获取{type_name}数据...")
        
        # 获取指定类型的数据
        all_notices = get_transport_data(notice_type)
        
        if all_notices:
            print(f"✅ 成功获取{type_name} {len(all_notices)} 条通知")
            
            # 筛选今天的通知
            today_notices = filter_today_notices(all_notices)
            print(f"📅 {type_name}今天的通知: {len(today_notices)} 条")
            
            if today_notices:
                # 保存到数据库
                save_notifications_to_db(today_notices, notice_type, date_str)
                new_notifications_found = True
                
                # 检查是否已发送过
                if not has_sent_today(date_str, notice_type):
                    # 格式化消息
                    message = format_type_messages(today_notices, notice_type)
                    
                    if message:
                        print(f"📱 正在发送{type_name}微信消息...")
                        result = xiaowang(message)
                        
                        if "error" not in result and result.get('code') == 0:
                            print(f"✅ {type_name}微信消息发送成功!")
                            print(f"📱 返回信息: {result.get('data', '发送成功')}")
                            
                            # 标记为已发送
                            mark_as_sent(date_str, notice_type, len(today_notices))
                        else:
                            print(f"❌ {type_name}微信消息发送失败")
                            if "error" in result:
                                print(f"   错误信息: {result.get('error')}")
                            else:
                                print(f"   返回码: {result.get('code')}")
                                print(f"   返回信息: {result.get('message', '未知错误')}")
                else:
                    print(f"📋 {type_name}今天已发送过，跳过推送")
        else:
            print(f"❌ {type_name}数据获取失败")
    
    if not new_notifications_found:
        print("📭 今天没有新的通知")
    
    # 显示今天所有通知的统计
    today_all_notifications = get_today_notifications_from_db(date_str)
    total_count = sum(len(notices) for notices in today_all_notifications.values())
    if total_count > 0:
        print(f"📊 今天总计: {total_count} 条通知")
        for notice_type, notices in today_all_notifications.items():
            if notices:
                type_name = NOTICE_TYPES[notice_type]['name']
                print(f"   {type_name}: {len(notices)} 条")

def main():
    """主函数 - 循环检测"""
    print("🚀 舟山交通数据循环检测启动")
    print(f"⏰ 检测间隔: {CHECK_INTERVAL} 秒")
    print(f"🕐 检测时间: {CHECK_START_HOUR}:00 - {CHECK_END_HOUR}:00")
    
    # 显示启用的通知类型
    enabled_types = get_all_enabled_notice_types()
    print("📋 启用的通知类型:")
    for notice_type in enabled_types:
        type_info = NOTICE_TYPES[notice_type]
        print(f"   {type_info['emoji']} {type_info['name']}")
    
    print(f"💾 数据库文件: {DB_FILE}")
    print("=" * 50)

    try:
        while True:
            current_time = datetime.now()

            # 检查是否在检测时间范围内
            if is_in_check_time():
                try:
                    check_and_send()
                except Exception as e:
                    print(f"❌ 检测过程中出现错误: {e}")
            else:
                print(f"[{current_time.strftime('%Y-%m-%d %H:%M:%S')}] 当前不在检测时间范围内")
                print(f"🕐 {get_next_check_time()}")

            print(f"\n💤 等待 {CHECK_INTERVAL} 秒后进行下次检测...")
            print("=" * 50)
            time.sleep(CHECK_INTERVAL)

    except KeyboardInterrupt:
        print("\n\n🛑 程序已手动停止")
    except Exception as e:
        print(f"\n\n💥 程序异常退出: {e}")

if __name__ == "__main__":
    main()