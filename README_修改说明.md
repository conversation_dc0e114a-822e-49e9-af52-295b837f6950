# 舟山交通数据监控系统 - 修改说明

## 主要修改内容

### 1. 支持多种通知类型
- **水路客运** (类型 "0") 🚢
- **交通信息** (类型 "1") 🚗  
- **天气预警** (类型 "2") 🌦️

### 2. 数据存储改为SQLite数据库
- 替换了原来的JSON文件存储方式
- 创建了两个数据表：
  - `notifications`: 存储通知记录
  - `sent_records`: 存储发送记录

### 3. 微信通知分类发送
- 每种类型的通知单独发送一条微信消息
- 消息格式包含类型图标和名称
- 按发现顺序给消息标序号

### 4. 新增功能
- 自动初始化数据库
- 防重复发送机制
- 按发现顺序标记通知序号
- 统计显示功能

## 配置说明

### 通知类型配置
```python
NOTICE_TYPES = {
    "0": {"name": "水路客运", "emoji": "🚢", "enabled": True},
    "1": {"name": "交通信息", "emoji": "🚗", "enabled": True},
    "2": {"name": "天气预警", "emoji": "🌦️", "enabled": True}
}
```

可以通过设置 `"enabled": False` 来禁用某种类型的通知。

### 数据库文件
- 文件名: `zsjt_notifications.db`
- 自动创建在程序运行目录

## 使用方法

1. 运行新的程序文件：
   ```bash
   python zsjt_send_wechat_new.py
   ```

2. 程序会自动：
   - 初始化数据库
   - 获取三种类型的通知数据
   - 分别发送微信消息
   - 记录发送状态

## 文件说明

- `zsjt_send_wechat_new.py`: 新的主程序文件
- `zsjt_notifications.db`: SQLite数据库文件（自动创建）
- 原有的JSON文件可以保留作为备份

## 主要改进

1. **更好的数据管理**: 使用SQLite数据库替代JSON文件
2. **防重复发送**: 每天每种类型只发送一次
3. **序号标记**: 按发现顺序给通知标序号
4. **分类发送**: 不同类型的通知分别发送
5. **配置灵活**: 可以轻松启用/禁用某种类型的通知
6. **统计功能**: 显示每天各类型通知的数量统计

## 注意事项

1. 确保Python环境已安装所需依赖：
   - requests
   - sqlite3 (Python内置)
   
2. 微信机器人接口地址和参数保持不变

3. 程序运行时会在当前目录创建数据库文件

4. 如需迁移历史数据，可以编写脚本将JSON数据导入SQLite数据库